# دليل البدء السريع - محلل صفقات Excel

## التشغيل السريع

### 1. تشغيل البرنامج
```bash
dotnet run
```

### 2. إنشاء بيانات تجريبية (اختياري)
```bash
cd TestDataGenerator
dotnet run
cd ..
```

### 3. خطوات الاستخدام
1. **اختر ملف Excel**: اضغط "استعراض" بجانب "ملف Excel المدخل"
2. **حدد ملف المخرج**: اضغط "استعراض" بجانب "ملف Excel المخرج" أو اتركه كما هو
3. **اضبط العمولة**: أدخل معدل العمولة (مثال: 0.1 للعمولة 10%)
4. **اضغط "تحليل الصفقات"**

## تنسيق ملف Excel المطلوب

### الأعمدة المطلوبة:
- **Id**: معرف الصفقة
- **Type**: نوع الصفقة (شراء، بيع، تحويل، إلخ)
- **Volume**: حجم الصفقة (رقم)
- **OpenTime**: وقت الافتتاح (تاريخ ووقت)
- **CloseTime**: وقت الإغلاق (تاريخ ووقت)

### الأعمدة الاختيارية:
- **OpenPrice**: سعر الافتتاح
- **ClosePrice**: سعر الإغلاق
- **ProfitLoss**: الربح أو الخسارة
- **Symbol**: رمز الأداة المالية
- **Comment**: تعليقات

## مثال سريع

### ملف Excel المدخل:
```
Id | Type | Volume | OpenTime            | CloseTime           
1  | شراء | 1.5    | 2024-01-01 09:00:00 | 2024-01-01 09:07:00
2  | بيع  | 2.0    | 2024-01-01 10:00:00 | 2024-01-01 10:03:00
```

### النتيجة:
- الصفقة الأولى: مدتها 7 دقائق ✅ (تخطت 5 دقائق)
- الصفقة الثانية: مدتها 3 دقائق ❌ (لم تخط 5 دقائق)

### تقرير المخرج:
- **إجمالي الصفقات**: 2
- **الصفقات المؤهلة**: 1
- **شراء**: حجم 1.5، بعد العمولة 0.15 (بعمولة 10%)

## نصائح سريعة

✅ **افعل**:
- تأكد من صحة تنسيق التواريخ
- استخدم أسماء أعمدة واضحة
- احفظ نسخة احتياطية من ملفك

❌ **لا تفعل**:
- لا تترك خلايا التاريخ فارغة
- لا تستخدم أحجام صفقات سالبة
- لا تخلط بين تنسيقات التاريخ

## حل المشاكل السريع

### "الملف غير موجود"
- تأكد من مسار الملف
- تأكد من امتداد الملف (.xlsx أو .xls)

### "بيانات غير صحيحة"
- تحقق من وجود الأعمدة المطلوبة
- تأكد من تنسيق التواريخ
- تأكد من أن الأحجام أرقام موجبة

### "لا توجد صفقات مؤهلة"
- قلل الحد الأدنى للمدة
- تحقق من النطاق الزمني المحدد
- تأكد من صحة حساب المدة

---

**للمزيد من التفاصيل، راجع ملف README.md**
