using System;

namespace ExcelTradeAnalyzer.Models
{
    /// <summary>
    /// إعدادات التحليل
    /// </summary>
    public class AnalysisSettings
    {
        /// <summary>
        /// مسار ملف Excel المدخل
        /// </summary>
        public string InputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// مسار ملف Excel المخرج
        /// </summary>
        public string OutputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// معدل العمولة
        /// </summary>
        public decimal CommissionRate { get; set; } = 0.1m;

        /// <summary>
        /// تاريخ البداية للتحليل (اختياري)
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// تاريخ النهاية للتحليل (اختياري)
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// الحد الأدنى لمدة الصفقة بالدقائق (افتراضي: 5)
        /// </summary>
        public double MinimumDurationMinutes { get; set; } = 5.0;

        /// <summary>
        /// اسم ورقة العمل في ملف Excel (افتراضي: الورقة الأولى)
        /// </summary>
        public string WorksheetName { get; set; } = string.Empty;

        /// <summary>
        /// رقم الصف الذي يحتوي على العناوين (افتراضي: 1)
        /// </summary>
        public int HeaderRow { get; set; } = 1;

        /// <summary>
        /// رقم الصف الذي تبدأ منه البيانات (افتراضي: 2)
        /// </summary>
        public int DataStartRow { get; set; } = 2;
    }
}
