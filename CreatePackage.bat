@echo off
chcp 65001 > nul
title إنشاء حزمة التوزيع - Creating Distribution Package

echo.
echo ========================================
echo    إنشاء حزمة توزيع محلل صفقات Excel
echo    Creating Excel Trade Analyzer Package
echo ========================================
echo.

REM Create distribution folder
set DIST_FOLDER=ExcelTradeAnalyzer_Distribution
if exist "%DIST_FOLDER%" rmdir /s /q "%DIST_FOLDER%"
mkdir "%DIST_FOLDER%"

echo جاري إنشاء حزمة التوزيع...
echo Creating distribution package...
echo.

REM Copy main project files
echo نسخ ملفات المشروع الأساسية...
echo Copying main project files...
copy "ExcelTradeAnalyzer.csproj" "%DIST_FOLDER%\"
copy "App.xaml" "%DIST_FOLDER%\"
copy "App.xaml.cs" "%DIST_FOLDER%\"
copy "MainWindow.xaml" "%DIST_FOLDER%\"
copy "MainWindow.xaml.cs" "%DIST_FOLDER%\"

REM Copy Models folder
echo نسخ مجلد النماذج...
echo Copying Models folder...
mkdir "%DIST_FOLDER%\Models"
copy "Models\*.cs" "%DIST_FOLDER%\Models\"

REM Copy Services folder
echo نسخ مجلد الخدمات...
echo Copying Services folder...
mkdir "%DIST_FOLDER%\Services"
copy "Services\*.cs" "%DIST_FOLDER%\Services\"

REM Copy Resources folder
echo نسخ مجلد الموارد...
echo Copying Resources folder...
mkdir "%DIST_FOLDER%\Resources"
if exist "Resources\*" copy "Resources\*" "%DIST_FOLDER%\Resources\"

REM Copy TestData folder
echo نسخ البيانات التجريبية...
echo Copying test data...
mkdir "%DIST_FOLDER%\TestData"
copy "TestData\*.csv" "%DIST_FOLDER%\TestData\"
if exist "TestData\*.xlsx" copy "TestData\*.xlsx" "%DIST_FOLDER%\TestData\"

REM Copy TestDataGenerator
echo نسخ مولد البيانات التجريبية...
echo Copying test data generator...
mkdir "%DIST_FOLDER%\TestDataGenerator"
copy "TestDataGenerator\*.cs" "%DIST_FOLDER%\TestDataGenerator\"
copy "TestDataGenerator\*.csproj" "%DIST_FOLDER%\TestDataGenerator\"

REM Copy documentation
echo نسخ الوثائق...
echo Copying documentation...
copy "README.md" "%DIST_FOLDER%\"
copy "QUICK_START.md" "%DIST_FOLDER%\"

REM Copy solution file
if exist "ExcelTradeAnalyzer.sln" copy "ExcelTradeAnalyzer.sln" "%DIST_FOLDER%\"

REM Copy batch files
echo نسخ ملفات التشغيل...
echo Copying run files...
copy "تشغيل البرنامج.bat" "%DIST_FOLDER%\"
copy "Run Excel Analyzer.bat" "%DIST_FOLDER%\"
copy "Build.bat" "%DIST_FOLDER%\"

REM Create installation instructions
echo إنشاء تعليمات التثبيت...
echo Creating installation instructions...

(
echo ========================================
echo    محلل صفقات Excel - Excel Trade Analyzer
echo ========================================
echo.
echo تعليمات التثبيت والتشغيل:
echo Installation and Running Instructions:
echo.
echo 1. متطلبات النظام / System Requirements:
echo    - Windows 10/11
echo    - .NET 8.0 Runtime
echo.
echo 2. التثبيت / Installation:
echo    أ. تحميل وتثبيت .NET 8.0 من:
echo    a. Download and install .NET 8.0 from:
echo       https://dotnet.microsoft.com/download/dotnet/8.0
echo.
echo    ب. استخراج جميع الملفات إلى مجلد
echo    b. Extract all files to a folder
echo.
echo 3. التشغيل / Running:
echo    أ. انقر نقراً مزدوجاً على "تشغيل البرنامج.bat"
echo    a. Double-click "تشغيل البرنامج.bat"
echo.
echo    أو / Or:
echo    ب. انقر نقراً مزدوجاً على "Run Excel Analyzer.bat"
echo    b. Double-click "Run Excel Analyzer.bat"
echo.
echo 4. البناء من المصدر / Build from Source:
echo    انقر نقراً مزدوجاً على "Build.bat"
echo    Double-click "Build.bat"
echo.
echo 5. البيانات التجريبية / Test Data:
echo    استخدم الملفات في مجلد TestData للاختبار
echo    Use files in TestData folder for testing
echo.
echo ========================================
echo للدعم الفني، راجع ملف README.md
echo For technical support, see README.md
echo ========================================
) > "%DIST_FOLDER%\تعليمات التثبيت - Installation Instructions.txt"

REM Create a simple ZIP using PowerShell
echo إنشاء ملف مضغوط...
echo Creating ZIP file...

powershell -Command "Compress-Archive -Path '%DIST_FOLDER%' -DestinationPath 'ExcelTradeAnalyzer_v1.0.zip' -Force"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo تم إنشاء حزمة التوزيع بنجاح!
    echo Distribution package created successfully!
    echo ========================================
    echo.
    echo الملف المضغوط: ExcelTradeAnalyzer_v1.0.zip
    echo ZIP file: ExcelTradeAnalyzer_v1.0.zip
    echo.
    echo مجلد التوزيع: %DIST_FOLDER%
    echo Distribution folder: %DIST_FOLDER%
    echo.
    echo يمكنك الآن نقل الملف المضغوط إلى أي جهاز آخر
    echo You can now transfer the ZIP file to any other computer
    echo.
) else (
    echo خطأ في إنشاء الملف المضغوط
    echo Error creating ZIP file
)

pause
