@echo off
echo تشغيل البرنامج...
echo Running application...

REM Try different .NET paths
set DOTNET_PATH1="C:\Program Files\dotnet\dotnet.exe"
set DOTNET_PATH2="C:\Program Files (x86)\dotnet\dotnet.exe"
set DOTNET_PATH3="dotnet"

echo جاري البحث عن .NET...
echo Looking for .NET...

if exist %DOTNET_PATH1% (
    echo تم العثور على .NET في: %DOTNET_PATH1%
    echo Found .NET at: %DOTNET_PATH1%
    %DOTNET_PATH1% run
    goto :end
)

if exist %DOTNET_PATH2% (
    echo تم العثور على .NET في: %DOTNET_PATH2%
    echo Found .NET at: %DOTNET_PATH2%
    %DOTNET_PATH2% run
    goto :end
)

echo محاولة استخدام dotnet من PATH...
echo Trying dotnet from PATH...
%DOTNET_PATH3% run

:end
if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل البرنامج
    echo Error running application
    echo.
    echo تأكد من تثبيت .NET 8.0 من:
    echo Make sure .NET 8.0 is installed from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
)
