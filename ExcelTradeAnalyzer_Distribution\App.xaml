<Application x:Class="ExcelTradeAnalyzer.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <!-- Modern Color Scheme -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="TextBrush" Color="#212121"/>
            <SolidColorBrush x:Key="SecondaryTextBrush" Color="#757575"/>
            
            <!-- Button Styles -->
            <Style x:Key="ModernButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="4"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#0D47A1"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <!-- TextBox Styles -->
            <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ScrollViewer x:Name="PART_ContentHost"
                                            Margin="{TemplateBinding Padding}"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
