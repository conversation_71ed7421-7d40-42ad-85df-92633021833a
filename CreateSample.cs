using System;
using System.IO;
using OfficeOpenXml;

class CreateSample
{
    static void Main()
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        var filePath = Path.Combine("TestData", "SampleTrades.xlsx");
        Directory.CreateDirectory("TestData");
        
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("الصفقات");

        // العناوين
        worksheet.Cells[1, 1].Value = "Id";
        worksheet.Cells[1, 2].Value = "Type";
        worksheet.Cells[1, 3].Value = "Volume";
        worksheet.Cells[1, 4].Value = "OpenTime";
        worksheet.Cells[1, 5].Value = "CloseTime";
        worksheet.Cells[1, 6].Value = "OpenPrice";
        worksheet.Cells[1, 7].Value = "ClosePrice";
        worksheet.Cells[1, 8].Value = "ProfitLoss";
        worksheet.Cells[1, 9].Value = "Symbol";
        worksheet.Cells[1, 10].Value = "Comment";

        // البيانات
        worksheet.Cells[2, 1].Value = "1";
        worksheet.Cells[2, 2].Value = "شراء";
        worksheet.Cells[2, 3].Value = 1.5;
        worksheet.Cells[2, 4].Value = new DateTime(2024, 1, 1, 9, 0, 0);
        worksheet.Cells[2, 5].Value = new DateTime(2024, 1, 1, 9, 7, 0);
        worksheet.Cells[2, 6].Value = 1.2500;
        worksheet.Cells[2, 7].Value = 1.2520;
        worksheet.Cells[2, 8].Value = 30.00;
        worksheet.Cells[2, 9].Value = "EURUSD";
        worksheet.Cells[2, 10].Value = "صفقة طويلة";

        worksheet.Cells[3, 1].Value = "2";
        worksheet.Cells[3, 2].Value = "بيع";
        worksheet.Cells[3, 3].Value = 2.0;
        worksheet.Cells[3, 4].Value = new DateTime(2024, 1, 1, 10, 0, 0);
        worksheet.Cells[3, 5].Value = new DateTime(2024, 1, 1, 10, 3, 0);
        worksheet.Cells[3, 6].Value = 1.2520;
        worksheet.Cells[3, 7].Value = 1.2510;
        worksheet.Cells[3, 8].Value = 20.00;
        worksheet.Cells[3, 9].Value = "EURUSD";
        worksheet.Cells[3, 10].Value = "صفقة قصيرة";

        worksheet.Cells[4, 1].Value = "3";
        worksheet.Cells[4, 2].Value = "شراء";
        worksheet.Cells[4, 3].Value = 3.2;
        worksheet.Cells[4, 4].Value = new DateTime(2024, 1, 1, 14, 0, 0);
        worksheet.Cells[4, 5].Value = new DateTime(2024, 1, 1, 14, 12, 0);
        worksheet.Cells[4, 6].Value = 1.2505;
        worksheet.Cells[4, 7].Value = 1.2495;
        worksheet.Cells[4, 8].Value = 32.00;
        worksheet.Cells[4, 9].Value = "EURUSD";
        worksheet.Cells[4, 10].Value = "صفقة ناجحة";

        worksheet.Cells[5, 1].Value = "4";
        worksheet.Cells[5, 2].Value = "تحويل";
        worksheet.Cells[5, 3].Value = 5.0;
        worksheet.Cells[5, 4].Value = new DateTime(2024, 1, 1, 16, 0, 0);
        worksheet.Cells[5, 5].Value = new DateTime(2024, 1, 1, 16, 15, 0);
        worksheet.Cells[5, 6].Value = 1.2500;
        worksheet.Cells[5, 7].Value = 1.2500;
        worksheet.Cells[5, 8].Value = 0.00;
        worksheet.Cells[5, 9].Value = "EURUSD";
        worksheet.Cells[5, 10].Value = "تحويل عملة";

        // تنسيق
        worksheet.Cells[2, 4, 5, 5].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
        worksheet.Cells[1, 1, 1, 10].Style.Font.Bold = true;
        worksheet.Cells.AutoFitColumns();

        package.SaveAs(new FileInfo(filePath));
        Console.WriteLine($"تم إنشاء الملف: {Path.GetFullPath(filePath)}");
    }
}
