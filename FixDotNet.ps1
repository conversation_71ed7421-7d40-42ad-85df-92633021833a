# Fix .NET Installation Issues
Write-Host "إصلاح مشاكل .NET - Fixing .NET Issues" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow

# Check .NET installation
Write-Host "فحص تثبيت .NET..." -ForegroundColor Cyan
Write-Host "Checking .NET installation..." -ForegroundColor Cyan

$dotnetPaths = @(
    "C:\Program Files\dotnet\dotnet.exe",
    "C:\Program Files (x86)\dotnet\dotnet.exe"
)

$foundDotnet = $false
foreach ($path in $dotnetPaths) {
    if (Test-Path $path) {
        Write-Host "تم العثور على .NET في: $path" -ForegroundColor Green
        Write-Host "Found .NET at: $path" -ForegroundColor Green
        $foundDotnet = $true
        
        try {
            $version = & $path --version 2>$null
            Write-Host "الإصدار: $version" -ForegroundColor Green
            Write-Host "Version: $version" -ForegroundColor Green
        } catch {
            Write-Host "خطأ في قراءة الإصدار" -ForegroundColor Red
            Write-Host "Error reading version" -ForegroundColor Red
        }
        break
    }
}

if (-not $foundDotnet) {
    Write-Host ".NET غير مثبت أو غير موجود" -ForegroundColor Red
    Write-Host ".NET is not installed or not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "يرجى تحميل وتثبيت .NET 8.0 من:" -ForegroundColor Yellow
    Write-Host "Please download and install .NET 8.0 from:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
    return
}

# Try to run the application directly
Write-Host ""
Write-Host "محاولة تشغيل البرنامج..." -ForegroundColor Cyan
Write-Host "Trying to run the application..." -ForegroundColor Cyan

try {
    # Change to project directory
    Set-Location "h:\New folder (3)"
    
    # Try to build first
    Write-Host "بناء المشروع..." -ForegroundColor Yellow
    Write-Host "Building project..." -ForegroundColor Yellow
    
    $buildResult = & $dotnetPaths[0] build 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "تم البناء بنجاح" -ForegroundColor Green
        Write-Host "Build successful" -ForegroundColor Green
        
        # Try to run
        Write-Host "تشغيل البرنامج..." -ForegroundColor Yellow
        Write-Host "Running application..." -ForegroundColor Yellow
        
        Start-Process $dotnetPaths[0] -ArgumentList "run" -WorkingDirectory (Get-Location)
        Write-Host "تم تشغيل البرنامج" -ForegroundColor Green
        Write-Host "Application started" -ForegroundColor Green
    } else {
        Write-Host "خطأ في البناء:" -ForegroundColor Red
        Write-Host "Build error:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
