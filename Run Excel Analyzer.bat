@echo off
chcp 65001 > nul
title Excel Trade Analyzer - محلل صفقات Excel

echo.
echo ========================================
echo    Excel Trade Analyzer - محلل صفقات Excel
echo ========================================
echo.

REM Change to the application directory
cd /d "%~dp0"

REM Try to run the built executable first
if exist "bin\Release\net8.0-windows\ExcelTradeAnalyzer.exe" (
    echo تشغيل النسخة المبنية...
    echo Running built version...
    echo.
    "bin\Release\net8.0-windows\ExcelTradeAnalyzer.exe"
    goto :end
)

REM If executable doesn't exist, try dotnet run
if exist "ExcelTradeAnalyzer.csproj" (
    echo النسخة المبنية غير موجودة، جاري تشغيل المشروع...
    echo Built version not found, running project...
    echo.
    
    REM Check if .NET is installed
    dotnet --version > nul 2>&1
    if %errorlevel% neq 0 (
        echo خطأ: .NET 8.0 غير مثبت على هذا الجهاز
        echo Error: .NET 8.0 is not installed on this system
        echo.
        echo يرجى تحميل وتثبيت .NET 8.0 من:
        echo Please download and install .NET 8.0 from:
        echo https://dotnet.microsoft.com/download/dotnet/8.0
        echo.
        pause
        exit /b 1
    )
    
    dotnet run --project ExcelTradeAnalyzer.csproj
    goto :end
)

REM If nothing works, show error
echo خطأ: لم يتم العثور على ملفات البرنامج
echo Error: Application files not found
echo.
echo تأكد من وجود الملفات التالية:
echo Make sure the following files exist:
echo - ExcelTradeAnalyzer.csproj
echo - أو bin\Release\net8.0-windows\ExcelTradeAnalyzer.exe
echo.
pause
exit /b 1

:end
if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo An error occurred while running the application
    echo.
    pause
)

exit /b %errorlevel%
