# دليل التوزيع - Distribution Guide

## 📦 إنشاء حزمة التوزيع

### الطريقة السريعة:
```bash
# تشغيل ملف إنشاء الحزمة
CreatePackage.bat
```

### الطريقة اليدوية:
1. **بناء البرنامج:**
   ```bash
   Build.bat
   ```

2. **إنشاء مجلد التوزيع:**
   - إنشاء مجلد جديد باسم `ExcelTradeAnalyzer_Distribution`
   - نسخ جميع الملفات المطلوبة

3. **ضغط المجلد:**
   ```bash
   # استخدام PowerShell
   Compress-Archive -Path "ExcelTradeAnalyzer_Distribution" -DestinationPath "ExcelTradeAnalyzer_v1.0.zip"
   ```

## 📋 الملفات المطلوبة للتوزيع

### ملفات المشروع الأساسية:
- `ExcelTradeAnalyzer.csproj`
- `App.xaml` و `App.xaml.cs`
- `MainWindow.xaml` و `MainWindow.xaml.cs`

### مجلدات المشروع:
- `Models/` - نماذج البيانات
- `Services/` - خدمات المعالجة
- `Resources/` - الموارد والأيقونات
- `TestData/` - البيانات التجريبية
- `TestDataGenerator/` - مولد البيانات التجريبية

### ملفات التشغيل:
- `تشغيل البرنامج.bat` - للتشغيل المباشر
- `Run Excel Analyzer.bat` - ملف تشغيل بديل
- `Build.bat` - لبناء البرنامج

### الوثائق:
- `README.md` - الدليل الشامل
- `QUICK_START.md` - دليل البدء السريع
- `تعليمات التثبيت - Installation Instructions.txt`

## 🚀 تعليمات التثبيت للمستخدم النهائي

### المتطلبات:
1. **نظام التشغيل:** Windows 10/11
2. **.NET 8.0 Runtime** - تحميل من: https://dotnet.microsoft.com/download/dotnet/8.0

### خطوات التثبيت:
1. **استخراج الملفات:**
   - استخراج محتويات الملف المضغوط إلى مجلد
   - مثال: `C:\Programs\ExcelTradeAnalyzer\`

2. **تثبيت .NET 8.0:**
   - تحميل وتثبيت .NET 8.0 Runtime
   - إعادة تشغيل الجهاز إذا لزم الأمر

3. **تشغيل البرنامج:**
   - نقر مزدوج على `تشغيل البرنامج.bat`
   - أو نقر مزدوج على `Run Excel Analyzer.bat`

## 🔧 استكشاف الأخطاء

### ".NET غير مثبت":
- تحميل وتثبيت .NET 8.0 Runtime
- التأكد من إعادة تشغيل الجهاز

### "ملفات البرنامج غير موجودة":
- التأكد من استخراج جميع الملفات
- التأكد من وجود ملف `ExcelTradeAnalyzer.csproj`

### "خطأ في تشغيل البرنامج":
- تشغيل `Build.bat` لبناء البرنامج محلياً
- التحقق من رسائل الخطأ في نافذة الأوامر

## 📁 هيكل حزمة التوزيع

```
ExcelTradeAnalyzer_v1.0.zip
├── ExcelTradeAnalyzer.csproj
├── App.xaml
├── App.xaml.cs
├── MainWindow.xaml
├── MainWindow.xaml.cs
├── Models/
│   ├── Trade.cs
│   ├── TradeAnalysisResult.cs
│   └── AnalysisSettings.cs
├── Services/
│   ├── ExcelService.cs
│   └── TradeAnalysisService.cs
├── Resources/
│   ├── icon.ico
│   └── icon.svg
├── TestData/
│   ├── SampleTrades.csv
│   └── SampleTrades.xlsx (إذا تم إنشاؤه)
├── TestDataGenerator/
│   ├── Program.cs
│   └── TestDataGenerator.csproj
├── تشغيل البرنامج.bat
├── Run Excel Analyzer.bat
├── Build.bat
├── README.md
├── QUICK_START.md
└── تعليمات التثبيت - Installation Instructions.txt
```

## 🎯 نصائح للتوزيع

### للمطورين:
- استخدام `Build.bat` لبناء نسخة محسنة
- اختبار الحزمة على جهاز نظيف
- تحديث رقم الإصدار في الملفات

### للمستخدمين:
- الاحتفاظ بنسخة احتياطية من البيانات
- قراءة `README.md` للتفاصيل الكاملة
- استخدام البيانات التجريبية للاختبار أولاً

## 📞 الدعم الفني

للحصول على المساعدة:
1. مراجعة ملف `README.md`
2. مراجعة ملف `QUICK_START.md`
3. التحقق من رسائل الخطأ في نافذة الأوامر
4. التأكد من تثبيت .NET 8.0 بشكل صحيح

---

**تم إنشاء هذا الدليل بواسطة Augment Agent**
