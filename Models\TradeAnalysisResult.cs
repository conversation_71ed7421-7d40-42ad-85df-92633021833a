using System.Collections.Generic;

namespace ExcelTradeAnalyzer.Models
{
    /// <summary>
    /// نتيجة تحليل الصفقات
    /// </summary>
    public class TradeAnalysisResult
    {
        /// <summary>
        /// إجمالي عدد الصفقات المحللة
        /// </summary>
        public int TotalTrades { get; set; }

        /// <summary>
        /// عدد الصفقات التي تخطت 5 دقائق
        /// </summary>
        public int TradesExceedingFiveMinutes { get; set; }

        /// <summary>
        /// الصفقات مجمعة حسب النوع مع إجمالي الحجم لكل نوع
        /// </summary>
        public Dictionary<string, decimal> TradesByType { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// الصفقات مجمعة حسب النوع مع إجمالي الحجم بعد ضرب العمولة
        /// </summary>
        public Dictionary<string, decimal> TradesByTypeWithCommission { get; set; } = new Dictionary<string, decimal>();

        /// <summary>
        /// معدل العمولة المستخدم
        /// </summary>
        public decimal CommissionRate { get; set; }

        /// <summary>
        /// النطاق الزمني المحلل (من)
        /// </summary>
        public System.DateTime? DateFrom { get; set; }

        /// <summary>
        /// النطاق الزمني المحلل (إلى)
        /// </summary>
        public System.DateTime? DateTo { get; set; }

        /// <summary>
        /// إجمالي الحجم لجميع الصفقات
        /// </summary>
        public decimal TotalVolume { get; set; }

        /// <summary>
        /// إجمالي الحجم بعد العمولة
        /// </summary>
        public decimal TotalVolumeWithCommission { get; set; }
    }
}
