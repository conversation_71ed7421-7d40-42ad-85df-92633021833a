@echo off
chcp 65001 > nul
title بناء البرنامج - Building Application

echo.
echo ========================================
echo    بناء محلل صفقات Excel
echo    Building Excel Trade Analyzer
echo ========================================
echo.

REM Check if .NET is installed
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 غير مثبت على هذا الجهاز
    echo Error: .NET 8.0 is not installed on this system
    echo.
    pause
    exit /b 1
)

echo جاري بناء البرنامج...
echo Building application...
echo.

REM Clean previous builds
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM Build in Release mode
echo خطوة 1: بناء المشروع...
echo Step 1: Building project...
dotnet build -c Release

if %errorlevel% neq 0 (
    echo.
    echo خطأ في بناء المشروع
    echo Error building project
    pause
    exit /b 1
)

echo.
echo خطوة 2: نشر البرنامج...
echo Step 2: Publishing application...
dotnet publish -c Release -o "Published" --self-contained false

if %errorlevel% neq 0 (
    echo.
    echo خطأ في نشر البرنامج
    echo Error publishing application
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء البرنامج بنجاح!
echo Application built successfully!
echo ========================================
echo.
echo الملفات المبنية في مجلد: Published
echo Built files are in folder: Published
echo.
echo يمكنك الآن تشغيل البرنامج من:
echo You can now run the application from:
echo Published\ExcelTradeAnalyzer.exe
echo.
pause
