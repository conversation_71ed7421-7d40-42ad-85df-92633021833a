using System;
using System.IO;
using OfficeOpenXml;

namespace ExcelTradeAnalyzer.TestData
{
    public class CreateSampleExcel
    {
        public static void CreateSampleFile(string filePath)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الصفقات");

            // إضافة العناوين
            worksheet.Cells[1, 1].Value = "Id";
            worksheet.Cells[1, 2].Value = "Type";
            worksheet.Cells[1, 3].Value = "Volume";
            worksheet.Cells[1, 4].Value = "OpenTime";
            worksheet.Cells[1, 5].Value = "CloseTime";
            worksheet.Cells[1, 6].Value = "OpenPrice";
            worksheet.Cells[1, 7].Value = "ClosePrice";
            worksheet.Cells[1, 8].Value = "ProfitLoss";
            worksheet.Cells[1, 9].Value = "Symbol";
            worksheet.Cells[1, 10].Value = "Comment";

            // إضافة البيانات التجريبية
            var sampleData = new object[,]
            {
                {"1", "شراء", 1.5, new DateTime(2024, 1, 1, 9, 0, 0), new DateTime(2024, 1, 1, 9, 3, 0), 1.2500, 1.2520, 30.00, "EURUSD", "صفقة قصيرة"},
                {"2", "بيع", 2.0, new DateTime(2024, 1, 1, 10, 0, 0), new DateTime(2024, 1, 1, 10, 7, 0), 1.2520, 1.2510, 20.00, "EURUSD", "صفقة طويلة"},
                {"3", "شراء", 0.8, new DateTime(2024, 1, 1, 11, 0, 0), new DateTime(2024, 1, 1, 11, 2, 0), 1.2510, 1.2505, -4.00, "EURUSD", "صفقة قصيرة جداً"},
                {"4", "بيع", 3.2, new DateTime(2024, 1, 1, 14, 0, 0), new DateTime(2024, 1, 1, 14, 12, 0), 1.2505, 1.2495, 32.00, "EURUSD", "صفقة طويلة"},
                {"5", "شراء", 1.0, new DateTime(2024, 1, 1, 15, 0, 0), new DateTime(2024, 1, 1, 15, 8, 0), 1.2495, 1.2500, 5.00, "EURUSD", "صفقة متوسطة"},
                {"6", "تحويل", 5.0, new DateTime(2024, 1, 1, 16, 0, 0), new DateTime(2024, 1, 1, 16, 15, 0), 1.2500, 1.2500, 0.00, "EURUSD", "تحويل عملة"},
                {"7", "شراء", 2.5, new DateTime(2024, 1, 2, 9, 0, 0), new DateTime(2024, 1, 2, 9, 6, 0), 1.2500, 1.2530, 75.00, "EURUSD", "صفقة ناجحة"},
                {"8", "بيع", 1.8, new DateTime(2024, 1, 2, 10, 0, 0), new DateTime(2024, 1, 2, 10, 4, 0), 1.2530, 1.2525, 9.00, "EURUSD", "صفقة قصيرة"},
                {"9", "شراء", 4.0, new DateTime(2024, 1, 2, 11, 0, 0), new DateTime(2024, 1, 2, 11, 20, 0), 1.2525, 1.2540, 60.00, "EURUSD", "صفقة طويلة جداً"},
                {"10", "بيع", 0.5, new DateTime(2024, 1, 2, 14, 0, 0), new DateTime(2024, 1, 2, 14, 3, 0), 1.2540, 1.2535, 2.50, "EURUSD", "صفقة صغيرة"},
                {"11", "شراء", 3.5, new DateTime(2024, 1, 3, 9, 0, 0), new DateTime(2024, 1, 3, 9, 25, 0), 1.2535, 1.2560, 87.50, "EURUSD", "صفقة طويلة ناجحة"},
                {"12", "تحويل", 2.2, new DateTime(2024, 1, 3, 11, 0, 0), new DateTime(2024, 1, 3, 11, 18, 0), 1.2560, 1.2560, 0.00, "EURUSD", "تحويل كبير"},
                {"13", "بيع", 1.2, new DateTime(2024, 1, 3, 14, 0, 0), new DateTime(2024, 1, 3, 14, 7, 0), 1.2560, 1.2550, 12.00, "EURUSD", "صفقة متوسطة"},
                {"14", "شراء", 6.0, new DateTime(2024, 1, 4, 8, 0, 0), new DateTime(2024, 1, 4, 8, 45, 0), 1.2550, 1.2580, 180.00, "EURUSD", "صفقة كبيرة"},
                {"15", "بيع", 0.3, new DateTime(2024, 1, 4, 10, 0, 0), new DateTime(2024, 1, 4, 10, 2, 0), 1.2580, 1.2575, 1.50, "EURUSD", "صفقة صغيرة جداً"}
            };

            // كتابة البيانات
            for (int i = 0; i < sampleData.GetLength(0); i++)
            {
                for (int j = 0; j < sampleData.GetLength(1); j++)
                {
                    worksheet.Cells[i + 2, j + 1].Value = sampleData[i, j];
                }
            }

            // تنسيق التواريخ
            worksheet.Cells[2, 4, 16, 4].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
            worksheet.Cells[2, 5, 16, 5].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";

            // تنسيق الأرقام
            worksheet.Cells[2, 3, 16, 3].Style.Numberformat.Format = "0.0000";
            worksheet.Cells[2, 6, 16, 8].Style.Numberformat.Format = "0.0000";

            // تنسيق العناوين
            using (var range = worksheet.Cells[1, 1, 1, 10])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            }

            // ضبط عرض الأعمدة
            worksheet.Cells.AutoFitColumns();

            // حفظ الملف
            var fileInfo = new FileInfo(filePath);
            package.SaveAs(fileInfo);
        }
    }
}
