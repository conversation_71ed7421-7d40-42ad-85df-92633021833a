using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Collections.Generic;
using Microsoft.Win32;

namespace SimpleExcelAnalyzer
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Title = "محلل صفقات Excel - Excel Trade Analyzer";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;

            var grid = new Grid();
            grid.Margin = new Thickness(20);

            // Define rows
            for (int i = 0; i < 6; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // Title
            var title = new TextBlock
            {
                Text = "محلل صفقات Excel - Excel Trade Analyzer",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            Grid.SetRow(title, 0);
            grid.Children.Add(title);

            // File selection
            var filePanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 10) };
            var fileLabel = new TextBlock { Text = "ملف Excel:", Width = 100, VerticalAlignment = VerticalAlignment.Center };
            var fileTextBox = new TextBox { Name = "FilePathTextBox", Width = 400, Margin = new Thickness(10, 0) };
            var browseButton = new Button { Content = "تصفح", Width = 80, Margin = new Thickness(10, 0) };
            
            browseButton.Click += (s, e) => {
                var dialog = new OpenFileDialog
                {
                    Filter = "Excel Files|*.xlsx;*.xls;*.csv|All Files|*.*"
                };
                if (dialog.ShowDialog() == true)
                {
                    fileTextBox.Text = dialog.FileName;
                }
            };

            filePanel.Children.Add(fileLabel);
            filePanel.Children.Add(fileTextBox);
            filePanel.Children.Add(browseButton);
            Grid.SetRow(filePanel, 1);
            grid.Children.Add(filePanel);

            // Commission rate
            var commissionPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 10) };
            var commissionLabel = new TextBlock { Text = "معدل العمولة:", Width = 100, VerticalAlignment = VerticalAlignment.Center };
            var commissionTextBox = new TextBox { Name = "CommissionTextBox", Text = "0.02", Width = 100, Margin = new Thickness(10, 0) };
            var percentLabel = new TextBlock { Text = "%", VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(5, 0) };

            commissionPanel.Children.Add(commissionLabel);
            commissionPanel.Children.Add(commissionTextBox);
            commissionPanel.Children.Add(percentLabel);
            Grid.SetRow(commissionPanel, 2);
            grid.Children.Add(commissionPanel);

            // Analyze button
            var analyzeButton = new Button
            {
                Content = "تحليل الصفقات",
                Width = 150,
                Height = 40,
                FontSize = 16,
                Margin = new Thickness(0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            analyzeButton.Click += (s, e) => {
                try
                {
                    var filePath = fileTextBox.Text;
                    if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    {
                        MessageBox.Show("يرجى اختيار ملف صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    if (!double.TryParse(commissionTextBox.Text, out double commission))
                    {
                        MessageBox.Show("يرجى إدخال معدل عمولة صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    AnalyzeFile(filePath, commission / 100);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            };

            Grid.SetRow(analyzeButton, 3);
            grid.Children.Add(analyzeButton);

            // Results
            var resultsLabel = new TextBlock
            {
                Text = "النتائج:",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 20, 0, 10)
            };
            Grid.SetRow(resultsLabel, 4);
            grid.Children.Add(resultsLabel);

            var resultsTextBox = new TextBox
            {
                Name = "ResultsTextBox",
                IsReadOnly = true,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                FontSize = 12
            };
            Grid.SetRow(resultsTextBox, 5);
            grid.Children.Add(resultsTextBox);

            Content = grid;

            // Store references for later use
            Tag = new { FileTextBox = fileTextBox, CommissionTextBox = commissionTextBox, ResultsTextBox = resultsTextBox };
        }

        private void AnalyzeFile(string filePath, double commissionRate)
        {
            var tag = (dynamic)Tag;
            var resultsTextBox = (TextBox)tag.ResultsTextBox;

            try
            {
                var lines = File.ReadAllLines(filePath);
                if (lines.Length < 2)
                {
                    resultsTextBox.Text = "الملف فارغ أو لا يحتوي على بيانات كافية";
                    return;
                }

                var results = new List<string>();
                results.Add("=== تحليل صفقات Excel ===");
                results.Add($"الملف: {Path.GetFileName(filePath)}");
                results.Add($"معدل العمولة: {commissionRate * 100:F2}%");
                results.Add("");

                // Simple CSV parsing
                var trades = new List<dynamic>();
                var headers = lines[0].Split(',');
                
                for (int i = 1; i < lines.Length; i++)
                {
                    var values = lines[i].Split(',');
                    if (values.Length >= headers.Length)
                    {
                        var trade = new Dictionary<string, string>();
                        for (int j = 0; j < headers.Length; j++)
                        {
                            trade[headers[j].Trim()] = values[j].Trim();
                        }
                        trades.Add(trade);
                    }
                }

                results.Add($"إجمالي الصفقات: {trades.Count}");
                
                // Simple analysis
                var totalVolume = 0.0;
                var validTrades = 0;

                foreach (var trade in trades)
                {
                    var tradeDict = (Dictionary<string, string>)trade;
                    
                    // Try to find volume column
                    var volumeKey = tradeDict.Keys.FirstOrDefault(k => 
                        k.ToLower().Contains("volume") || 
                        k.ToLower().Contains("حجم") ||
                        k.ToLower().Contains("quantity") ||
                        k.ToLower().Contains("كمية"));

                    if (volumeKey != null && double.TryParse(tradeDict[volumeKey], out double volume))
                    {
                        totalVolume += volume;
                        validTrades++;
                    }
                }

                results.Add($"الصفقات الصحيحة: {validTrades}");
                results.Add($"إجمالي الحجم: {totalVolume:F2}");
                results.Add($"العمولة المحسوبة: {totalVolume * commissionRate:F2}");
                results.Add("");
                results.Add("=== تفاصيل الأعمدة ===");
                
                foreach (var header in headers)
                {
                    results.Add($"- {header.Trim()}");
                }

                results.Add("");
                results.Add("تم التحليل بنجاح!");

                resultsTextBox.Text = string.Join(Environment.NewLine, results);
            }
            catch (Exception ex)
            {
                resultsTextBox.Text = $"خطأ في التحليل: {ex.Message}";
            }
        }
    }

    public class App : Application
    {
        [STAThread]
        public static void Main()
        {
            var app = new App();
            app.Run(new MainWindow());
        }
    }
}
