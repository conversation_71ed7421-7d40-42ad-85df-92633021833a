@echo off
title محلل صفقات Excel - Excel Trade Analyzer

echo.
echo ========================================
echo    محلل صفقات Excel
echo    Excel Trade Analyzer  
echo ========================================
echo.

echo تشغيل البرنامج...
echo Starting application...
echo.

REM Kill any existing instances first
taskkill /F /IM dotnet.exe 2>nul

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start the application
start "" "C:\Program Files\dotnet\dotnet.exe" run --project ExcelTradeAnalyzer.csproj

echo.
echo تم تشغيل البرنامج
echo Application started
echo.
echo إذا لم تظهر نافذة البرنامج، تحقق من:
echo If the application window doesn't appear, check:
echo 1. تثبيت .NET 8.0
echo 1. .NET 8.0 installation
echo 2. وجود ملفات المشروع
echo 2. Project files existence
echo.

timeout /t 5 /nobreak >nul
