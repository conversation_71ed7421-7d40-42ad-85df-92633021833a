using System;
using System.Windows;

namespace TestApp
{
    class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Console.WriteLine("محاولة تشغيل تطبيق WPF...");
                Console.WriteLine("Trying to run WPF application...");
                
                var app = new Application();
                var window = new Window
                {
                    Title = "اختبار - Test",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };
                
                window.Content = new System.Windows.Controls.TextBlock
                {
                    Text = "البرنامج يعمل بنجاح!\nApplication is working!",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 16
                };
                
                app.Run(window);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ: {ex.Message}");
                Console.WriteLine($"Error: {ex.Message}");
                Console.ReadKey();
            }
        }
    }
}
