using System;

namespace ExcelTradeAnalyzer.Models
{
    /// <summary>
    /// يمثل صفقة واحدة من ملف Excel
    /// </summary>
    public class Trade
    {
        /// <summary>
        /// معرف الصفقة
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// نوع الصفقة (مثل: شراء، بيع، تحويل، إلخ)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// حجم الصفقة
        /// </summary>
        public decimal Volume { get; set; }

        /// <summary>
        /// وقت افتتاح الصفقة
        /// </summary>
        public DateTime OpenTime { get; set; }

        /// <summary>
        /// وقت إغلاق الصفقة
        /// </summary>
        public DateTime CloseTime { get; set; }

        /// <summary>
        /// مدة الصفقة بالدقائق
        /// </summary>
        public double DurationInMinutes => (CloseTime - OpenTime).TotalMinutes;

        /// <summary>
        /// هل تخطت الصفقة 5 دقائق؟
        /// </summary>
        public bool ExceedsFiveMinutes => DurationInMinutes > 5;

        /// <summary>
        /// سعر الافتتاح
        /// </summary>
        public decimal OpenPrice { get; set; }

        /// <summary>
        /// سعر الإغلاق
        /// </summary>
        public decimal ClosePrice { get; set; }

        /// <summary>
        /// الربح أو الخسارة
        /// </summary>
        public decimal ProfitLoss { get; set; }

        /// <summary>
        /// رمز الأداة المالية
        /// </summary>
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// تعليقات إضافية
        /// </summary>
        public string Comment { get; set; } = string.Empty;
    }
}
