using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ExcelTradeAnalyzer.Models;
using ExcelTradeAnalyzer.Services;

namespace ExcelTradeAnalyzer
{
    public partial class MainWindow : Window
    {
        private readonly ExcelService _excelService;
        private readonly TradeAnalysisService _analysisService;
        private List<Trade> _currentTrades;
        private TradeAnalysisResult _currentResult;

        public MainWindow()
        {
            InitializeComponent();
            _excelService = new ExcelService();
            _analysisService = new TradeAnalysisService();
            _currentTrades = new List<Trade>();
            
            // تعيين مسار افتراضي لملف المخرج
            OutputFileTextBox.Text = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                $"تحليل_الصفقات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }

        private void BrowseInputButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف Excel",
                Filter = "Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                InputFileTextBox.Text = openFileDialog.FileName;
                
                // تحديث مسار الملف المخرج تلقائياً
                var directory = Path.GetDirectoryName(openFileDialog.FileName);
                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(openFileDialog.FileName);
                OutputFileTextBox.Text = Path.Combine(directory, 
                    $"{fileNameWithoutExtension}_تحليل_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
            }
        }

        private void BrowseOutputButton_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "حفظ تقرير التحليل",
                Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                FilterIndex = 1,
                FileName = "تحليل_الصفقات.xlsx"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                OutputFileTextBox.Text = saveFileDialog.FileName;
            }
        }

        private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة المدخلات
                if (!ValidateInputs())
                    return;

                // تعطيل الواجهة أثناء المعالجة
                SetUIEnabled(false);
                UpdateStatus("جاري قراءة ملف Excel...");
                ShowProgress(true);

                // إعداد التحليل
                var settings = CreateAnalysisSettings();

                // قراءة البيانات
                await Task.Run(() =>
                {
                    _currentTrades = _excelService.ReadTradesFromExcel(InputFileTextBox.Text, settings);
                });

                UpdateStatus($"تم قراءة {_currentTrades.Count} صفقة. جاري التحليل...");

                // التحقق من صحة البيانات
                var validationErrors = _analysisService.ValidateTradeData(_currentTrades);
                if (validationErrors.Any())
                {
                    var errorMessage = "تحذيرات في البيانات:\n" + string.Join("\n", validationErrors);
                    MessageBox.Show(errorMessage, "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                // تحليل البيانات
                await Task.Run(() =>
                {
                    _currentResult = _analysisService.AnalyzeTrades(_currentTrades, settings);
                });

                // عرض النتائج
                DisplayResults();

                // حفظ النتائج
                UpdateStatus("جاري حفظ النتائج...");
                await Task.Run(() =>
                {
                    _excelService.WriteAnalysisResultToExcel(_currentResult, OutputFileTextBox.Text);
                });

                UpdateStatus($"تم الانتهاء بنجاح! تم حفظ التقرير في: {OutputFileTextBox.Text}");
                
                // عرض رسالة نجاح
                var result = MessageBox.Show(
                    $"تم تحليل الصفقات بنجاح!\n\nإجمالي الصفقات: {_currentResult.TotalTrades}\n" +
                    $"الصفقات المؤهلة: {_currentResult.TradesExceedingFiveMinutes}\n" +
                    $"إجمالي الحجم بعد العمولة: {_currentResult.TotalVolumeWithCommission:N4}\n\n" +
                    "هل تريد فتح ملف التقرير؟",
                    "تم بنجاح",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = OutputFileTextBox.Text,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                UpdateStatus("حدث خطأ أثناء المعالجة");
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SetUIEnabled(true);
                ShowProgress(false);
            }
        }

        private bool ValidateInputs()
        {
            if (string.IsNullOrWhiteSpace(InputFileTextBox.Text))
            {
                MessageBox.Show("يرجى اختيار ملف Excel المدخل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!File.Exists(InputFileTextBox.Text))
            {
                MessageBox.Show("الملف المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(OutputFileTextBox.Text))
            {
                MessageBox.Show("يرجى تحديد مسار ملف المخرج", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(CommissionRateTextBox.Text, out decimal commission) || commission < 0)
            {
                MessageBox.Show("يرجى إدخال معدل عمولة صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!double.TryParse(MinDurationTextBox.Text, out double duration) || duration < 0)
            {
                MessageBox.Show("يرجى إدخال مدة صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private AnalysisSettings CreateAnalysisSettings()
        {
            return new AnalysisSettings
            {
                InputFilePath = InputFileTextBox.Text,
                OutputFilePath = OutputFileTextBox.Text,
                CommissionRate = decimal.Parse(CommissionRateTextBox.Text),
                DateFrom = DateFromPicker.SelectedDate,
                DateTo = DateToPicker.SelectedDate,
                MinimumDurationMinutes = double.Parse(MinDurationTextBox.Text),
                WorksheetName = WorksheetNameTextBox.Text?.Trim() ?? string.Empty
            };
        }

        private void DisplayResults()
        {
            if (_currentResult == null)
                return;

            // عرض الملخص
            var summary = $"إجمالي الصفقات: {_currentResult.TotalTrades:N0}\n" +
                         $"الصفقات المؤهلة: {_currentResult.TradesExceedingFiveMinutes:N0}\n" +
                         $"معدل العمولة: {_currentResult.CommissionRate:P4}\n" +
                         $"إجمالي الحجم: {_currentResult.TotalVolume:N4}\n" +
                         $"إجمالي الحجم بعد العمولة: {_currentResult.TotalVolumeWithCommission:N4}\n";

            if (_currentResult.DateFrom.HasValue)
                summary += $"من تاريخ: {_currentResult.DateFrom.Value:yyyy-MM-dd}\n";
            
            if (_currentResult.DateTo.HasValue)
                summary += $"إلى تاريخ: {_currentResult.DateTo.Value:yyyy-MM-dd}\n";

            SummaryTextBlock.Text = summary;

            // عرض التفاصيل
            var details = _currentResult.TradesByType.Select(kvp => new
            {
                Type = kvp.Key,
                Volume = kvp.Value,
                VolumeWithCommission = _currentResult.TradesByTypeWithCommission.GetValueOrDefault(kvp.Key, 0)
            }).OrderByDescending(x => x.Volume).ToList();

            DetailsDataGrid.ItemsSource = details;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            InputFileTextBox.Text = string.Empty;
            WorksheetNameTextBox.Text = string.Empty;
            CommissionRateTextBox.Text = "0.1";
            MinDurationTextBox.Text = "5";
            DateFromPicker.SelectedDate = null;
            DateToPicker.SelectedDate = null;
            SummaryTextBlock.Text = "لم يتم تحليل أي بيانات بعد...";
            DetailsDataGrid.ItemsSource = null;
            
            OutputFileTextBox.Text = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                $"تحليل_الصفقات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
            
            UpdateStatus("تم مسح البيانات");
        }

        private void SetUIEnabled(bool enabled)
        {
            AnalyzeButton.IsEnabled = enabled;
            BrowseInputButton.IsEnabled = enabled;
            BrowseOutputButton.IsEnabled = enabled;
            ClearButton.IsEnabled = enabled;
        }

        private void ShowProgress(bool show)
        {
            ProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            if (show)
            {
                ProgressBar.IsIndeterminate = true;
            }
        }

        private void UpdateStatus(string message)
        {
            StatusTextBlock.Text = message;
        }
    }
}
