# محلل صفقات Excel - Excel Trade Analyzer

برنامج احترافي لتحليل صفقات Excel وحساب العمولات بشكل تلقائي.

## المميزات

### الوظائف الأساسية
- ✅ قراءة ملفات Excel (.xlsx, .xls)
- ✅ تصفية الصفقات التي تخطت مدة محددة (افتراضي: 5 دقائق)
- ✅ تصنيف الصفقات حسب النوع وجمع أحجامها
- ✅ حساب العمولات تلقائياً
- ✅ تحديد النطاق الزمني للتحليل
- ✅ تصدير النتائج إلى ملف Excel جديد

### الواجهة
- 🎨 واجهة احترافية وسهلة الاستخدام
- 🌐 دعم اللغة العربية
- 📊 عرض النتائج في الوقت الفعلي
- 📈 جداول تفاعلية لعرض التفاصيل

## متطلبات التشغيل

- Windows 10/11
- .NET 8.0 Runtime
- Microsoft Excel (لفتح الملفات المُصدرة)

## كيفية الاستخدام

### 1. تشغيل البرنامج
```bash
dotnet run
```

### 2. إعداد الملفات
- **ملف Excel المدخل**: اختر ملف Excel الذي يحتوي على بيانات الصفقات
- **ملف Excel المخرج**: حدد مكان حفظ تقرير التحليل
- **اسم ورقة العمل**: (اختياري) حدد اسم ورقة العمل المراد تحليلها

### 3. إعدادات التحليل
- **معدل العمولة**: أدخل معدل العمولة (مثال: 0.1 = 10%)
- **النطاق الزمني**: (اختياري) حدد فترة زمنية محددة للتحليل
- **الحد الأدنى للمدة**: المدة بالدقائق لتصفية الصفقات (افتراضي: 5 دقائق)

### 4. تشغيل التحليل
اضغط على زر "تحليل الصفقات" لبدء المعالجة.

## تنسيق ملف Excel المدخل

يجب أن يحتوي ملف Excel على الأعمدة التالية:

| العمود | الاسم الإنجليزي | الاسم العربي | النوع | مطلوب |
|--------|-----------------|--------------|-------|--------|
| Id | Id | معرف | نص | ✅ |
| Type | Type | نوع | نص | ✅ |
| Volume | Volume | حجم | رقم | ✅ |
| OpenTime | OpenTime | وقت الافتتاح | تاريخ ووقت | ✅ |
| CloseTime | CloseTime | وقت الإغلاق | تاريخ ووقت | ✅ |
| OpenPrice | OpenPrice | سعر الافتتاح | رقم | اختياري |
| ClosePrice | ClosePrice | سعر الإغلاق | رقم | اختياري |
| ProfitLoss | ProfitLoss | الربح/الخسارة | رقم | اختياري |
| Symbol | Symbol | رمز | نص | اختياري |
| Comment | Comment | تعليق | نص | اختياري |

### مثال على البيانات:
```
Id    Type    Volume  OpenTime             CloseTime            OpenPrice  ClosePrice  ProfitLoss  Symbol   Comment
1     شراء    1.5     2024-01-01 09:00:00  2024-01-01 09:07:00  1.2500     1.2520      30.00       EURUSD   صفقة ناجحة
2     بيع     2.0     2024-01-01 10:00:00  2024-01-01 10:03:00  1.2520     1.2510      20.00       EURUSD   صفقة قصيرة
```

## تقرير النتائج

يتم إنشاء ملف Excel يحتوي على:

### ورقة "ملخص التحليل"
- تاريخ التقرير
- النطاق الزمني المحلل
- معدل العمولة المستخدم
- إجمالي الصفقات
- عدد الصفقات المؤهلة
- إجمالي الحجم قبل وبعد العمولة

### ورقة "التفاصيل حسب النوع"
- تصنيف الصفقات حسب النوع
- إجمالي الحجم لكل نوع
- الحجم بعد تطبيق العمولة
- النسبة المئوية لكل نوع

## البيانات التجريبية

يمكنك إنشاء ملف Excel تجريبي باستخدام:
```bash
cd TestDataGenerator
dotnet run
```

سيتم إنشاء ملف `TestData/SampleTrades.xlsx` يحتوي على بيانات تجريبية للاختبار.

## الدعم الفني

### الأخطاء الشائعة
1. **"الملف غير موجود"**: تأكد من صحة مسار الملف
2. **"ورقة العمل غير موجودة"**: تأكد من اسم ورقة العمل أو اتركه فارغاً
3. **"بيانات غير صحيحة"**: تأكد من تنسيق البيانات حسب الجدول أعلاه

### نصائح للأداء الأفضل
- استخدم ملفات Excel بحجم معقول (أقل من 100,000 صف)
- تأكد من صحة تنسيق التواريخ
- احفظ نسخة احتياطية من ملفاتك قبل المعالجة

## الترخيص

هذا البرنامج مجاني للاستخدام الشخصي والتجاري.

## التطوير

### البنية التقنية
- **Framework**: .NET 8.0 WPF
- **مكتبة Excel**: EPPlus 7.0
- **اللغة**: C#

### هيكل المشروع
```
ExcelTradeAnalyzer/
├── Models/              # نماذج البيانات
├── Services/            # خدمات المعالجة
├── MainWindow.xaml      # الواجهة الرئيسية
├── App.xaml            # إعدادات التطبيق
└── TestData/           # البيانات التجريبية
```

---

**تم تطوير هذا البرنامج بواسطة Augment Agent**
