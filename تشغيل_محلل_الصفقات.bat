@echo off
chcp 65001 >nul
title محلل صفقات Excel - Excel Trade Analyzer

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    محلل صفقات Excel                         ║
echo ║                  Excel Trade Analyzer                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تشغيل البرنامج...
echo 🔄 Starting application...
echo.

REM Check if .NET is available
where dotnet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت أو غير متاح
    echo ❌ .NET is not installed or available
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 8.0 من:
    echo 📥 Please download and install .NET 8.0 from:
    echo 🌐 https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM Kill any existing instances
taskkill /F /IM dotnet.exe 2>nul >nul

REM Wait a moment
timeout /t 1 /nobreak >nul

REM Start the application
echo ✅ تم العثور على .NET
echo ✅ Found .NET
echo.
echo 🚀 تشغيل محلل صفقات Excel...
echo 🚀 Launching Excel Trade Analyzer...
echo.

start "" dotnet run --project ExcelTradeAnalyzer.csproj

REM Wait to see if it starts successfully
timeout /t 3 /nobreak >nul

echo ✅ تم تشغيل البرنامج بنجاح!
echo ✅ Application started successfully!
echo.
echo 📋 ملاحظات مهمة:
echo 📋 Important notes:
echo.
echo • يمكنك استخدام ملف sample_trades.csv للاختبار
echo • You can use sample_trades.csv file for testing
echo.
echo • البرنامج يدعم ملفات Excel (.xlsx, .xls) و CSV
echo • The application supports Excel (.xlsx, .xls) and CSV files
echo.
echo • معدل العمولة الافتراضي هو 0.02%
echo • Default commission rate is 0.02%
echo.

timeout /t 5 /nobreak >nul
