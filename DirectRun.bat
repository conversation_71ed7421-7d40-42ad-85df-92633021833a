@echo off
echo ========================================
echo تشغيل محلل صفقات Excel
echo Running Excel Trade Analyzer
echo ========================================
echo.

REM Set the exact .NET path
set DOTNET_EXE="C:\Program Files\dotnet\dotnet.exe"

echo فحص وجود .NET...
echo Checking for .NET...

if not exist %DOTNET_EXE% (
    echo .NET غير موجود في المسار المتوقع
    echo .NET not found at expected path
    echo المسار المتوقع: %DOTNET_EXE%
    echo Expected path: %DOTNET_EXE%
    echo.
    echo يرجى تثبيت .NET 8.0 من:
    echo Please install .NET 8.0 from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo تم العثور على .NET
echo Found .NET

echo.
echo تشغيل البرنامج الأصلي...
echo Running original application...
echo.

REM Change to project directory
cd /d "%~dp0"

REM Try to run the original application
%DOTNET_EXE% run --project ExcelTradeAnalyzer.csproj

if %errorlevel% neq 0 (
    echo.
    echo فشل تشغيل البرنامج الأصلي، محاولة تشغيل النسخة المبسطة...
    echo Failed to run original app, trying simple version...
    echo.
    
    %DOTNET_EXE% run --project SimpleExcelAnalyzer.csproj
    
    if %errorlevel% neq 0 (
        echo.
        echo فشل تشغيل جميع الإصدارات
        echo Failed to run all versions
        echo.
        echo تحقق من:
        echo Check:
        echo 1. تثبيت .NET 8.0
        echo 1. .NET 8.0 installation
        echo 2. وجود ملفات المشروع
        echo 2. Project files existence
        echo.
        pause
    )
)

echo.
echo انتهى التشغيل
echo Execution completed
pause
