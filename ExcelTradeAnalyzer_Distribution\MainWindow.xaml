<Window x:Class="ExcelTradeAnalyzer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="محلل صفقات Excel - Excel Trade Analyzer" 
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" 
                CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="محلل صفقات Excel" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="تحليل الصفقات وحساب العمولات بشكل احترافي" 
                          FontSize="14" Foreground="White" 
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Input Section -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceBrush}" 
                CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="إعدادات الملف" FontSize="16" FontWeight="Bold" 
                              Foreground="{StaticResource TextBrush}" Margin="0,0,0,10"/>
                    
                    <!-- Input File -->
                    <TextBlock Text="ملف Excel المدخل:" Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox x:Name="InputFileTextBox" Grid.Column="0" 
                                Style="{StaticResource ModernTextBoxStyle}" 
                                IsReadOnly="True"/>
                        <Button x:Name="BrowseInputButton" Grid.Column="1" 
                               Content="استعراض" Style="{StaticResource ModernButtonStyle}"
                               Click="BrowseInputButton_Click" Margin="5,0,0,0"/>
                    </Grid>

                    <!-- Output File -->
                    <TextBlock Text="ملف Excel المخرج:" Margin="0,15,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox x:Name="OutputFileTextBox" Grid.Column="0" 
                                Style="{StaticResource ModernTextBoxStyle}"/>
                        <Button x:Name="BrowseOutputButton" Grid.Column="1" 
                               Content="استعراض" Style="{StaticResource ModernButtonStyle}"
                               Click="BrowseOutputButton_Click" Margin="5,0,0,0"/>
                    </Grid>

                    <!-- Worksheet Name -->
                    <TextBlock Text="اسم ورقة العمل (اختياري):" Margin="0,15,0,5"/>
                    <TextBox x:Name="WorksheetNameTextBox" 
                            Style="{StaticResource ModernTextBoxStyle}"/>
                </StackPanel>

                <!-- Right Column -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="إعدادات التحليل" FontSize="16" FontWeight="Bold" 
                              Foreground="{StaticResource TextBrush}" Margin="0,0,0,10"/>
                    
                    <!-- Commission Rate -->
                    <TextBlock Text="معدل العمولة:" Margin="0,0,0,5"/>
                    <TextBox x:Name="CommissionRateTextBox" 
                            Style="{StaticResource ModernTextBoxStyle}" 
                            Text="0.1"/>

                    <!-- Date Range -->
                    <TextBlock Text="النطاق الزمني (اختياري):" Margin="0,15,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <DatePicker x:Name="DateFromPicker" Grid.Column="0" 
                                   Margin="0,0,5,0"/>
                        <TextBlock Grid.Column="1" Text="إلى" 
                                  VerticalAlignment="Center" Margin="5"/>
                        <DatePicker x:Name="DateToPicker" Grid.Column="2" 
                                   Margin="5,0,0,0"/>
                    </Grid>

                    <!-- Minimum Duration -->
                    <TextBlock Text="الحد الأدنى للمدة (دقيقة):" Margin="0,15,0,5"/>
                    <TextBox x:Name="MinDurationTextBox" 
                            Style="{StaticResource ModernTextBoxStyle}" 
                            Text="5"/>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" Margin="0,20,0,0" 
                               HorizontalAlignment="Center">
                        <Button x:Name="AnalyzeButton" Content="تحليل الصفقات" 
                               Style="{StaticResource ModernButtonStyle}"
                               Click="AnalyzeButton_Click" Margin="0,0,10,0"/>
                        <Button x:Name="ClearButton" Content="مسح البيانات" 
                               Style="{StaticResource ModernButtonStyle}"
                               Background="{StaticResource SecondaryBrush}"
                               Click="ClearButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Results Section -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceBrush}" 
                CornerRadius="8" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Summary -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="ملخص النتائج" FontSize="16" FontWeight="Bold" 
                              Foreground="{StaticResource TextBrush}" Margin="0,0,0,10"/>
                    <ScrollViewer MaxHeight="300">
                        <TextBlock x:Name="SummaryTextBlock" 
                                  TextWrapping="Wrap" 
                                  FontFamily="Consolas"
                                  Background="#F8F8F8" 
                                  Padding="10" 
                                  Text="لم يتم تحليل أي بيانات بعد..."/>
                    </ScrollViewer>
                </StackPanel>

                <!-- Details -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="التفاصيل حسب النوع" FontSize="16" FontWeight="Bold" 
                              Foreground="{StaticResource TextBrush}" Margin="0,0,0,10"/>
                    <DataGrid x:Name="DetailsDataGrid" 
                             AutoGenerateColumns="False" 
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="White"
                             MaxHeight="300">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="نوع الصفقة" 
                                              Binding="{Binding Type}" Width="*"/>
                            <DataGridTextColumn Header="الحجم" 
                                              Binding="{Binding Volume, StringFormat=N4}" Width="80"/>
                            <DataGridTextColumn Header="بعد العمولة" 
                                              Binding="{Binding VolumeWithCommission, StringFormat=N4}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="{StaticResource PrimaryBrush}" 
                CornerRadius="4" Padding="10" Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="StatusTextBlock" Grid.Column="0" 
                          Text="جاهز للاستخدام" 
                          Foreground="White" VerticalAlignment="Center"/>
                <ProgressBar x:Name="ProgressBar" Grid.Column="1" 
                            Width="200" Height="20" 
                            Visibility="Collapsed"/>
            </Grid>
        </Border>
    </Grid>
</Window>
