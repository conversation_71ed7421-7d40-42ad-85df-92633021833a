# Create Distribution Package for Excel Trade Analyzer
Write-Host "========================================" -ForegroundColor Green
Write-Host "   إنشاء حزمة توزيع محلل صفقات Excel" -ForegroundColor Green
Write-Host "   Creating Excel Trade Analyzer Package" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Set distribution folder name
$distFolder = "ExcelTradeAnalyzer_Distribution"

# Remove existing distribution folder
if (Test-Path $distFolder) {
    Write-Host "إزالة المجلد السابق..." -ForegroundColor Yellow
    Write-Host "Removing previous folder..." -ForegroundColor Yellow
    Remove-Item $distFolder -Recurse -Force
}

# Create distribution folder
Write-Host "إنشاء مجلد التوزيع..." -ForegroundColor Cyan
Write-Host "Creating distribution folder..." -ForegroundColor Cyan
New-Item -ItemType Directory -Name $distFolder | Out-Null

# Copy main project files
Write-Host "نسخ ملفات المشروع الأساسية..." -ForegroundColor Cyan
Write-Host "Copying main project files..." -ForegroundColor Cyan
Copy-Item "ExcelTradeAnalyzer.csproj" $distFolder
Copy-Item "App.xaml" $distFolder
Copy-Item "App.xaml.cs" $distFolder
Copy-Item "MainWindow.xaml" $distFolder
Copy-Item "MainWindow.xaml.cs" $distFolder

# Copy Models folder
Write-Host "نسخ مجلد النماذج..." -ForegroundColor Cyan
Write-Host "Copying Models folder..." -ForegroundColor Cyan
Copy-Item "Models" $distFolder -Recurse

# Copy Services folder
Write-Host "نسخ مجلد الخدمات..." -ForegroundColor Cyan
Write-Host "Copying Services folder..." -ForegroundColor Cyan
Copy-Item "Services" $distFolder -Recurse

# Copy Resources folder
Write-Host "نسخ مجلد الموارد..." -ForegroundColor Cyan
Write-Host "Copying Resources folder..." -ForegroundColor Cyan
if (Test-Path "Resources") {
    Copy-Item "Resources" $distFolder -Recurse
}

# Copy TestData folder
Write-Host "نسخ البيانات التجريبية..." -ForegroundColor Cyan
Write-Host "Copying test data..." -ForegroundColor Cyan
Copy-Item "TestData" $distFolder -Recurse

# Copy TestDataGenerator
Write-Host "نسخ مولد البيانات التجريبية..." -ForegroundColor Cyan
Write-Host "Copying test data generator..." -ForegroundColor Cyan
Copy-Item "TestDataGenerator" $distFolder -Recurse

# Copy documentation
Write-Host "نسخ الوثائق..." -ForegroundColor Cyan
Write-Host "Copying documentation..." -ForegroundColor Cyan
Copy-Item "README.md" $distFolder
Copy-Item "QUICK_START.md" $distFolder
Copy-Item "DISTRIBUTION_GUIDE.md" $distFolder

# Copy solution file
if (Test-Path "ExcelTradeAnalyzer.sln") {
    Copy-Item "ExcelTradeAnalyzer.sln" $distFolder
}

# Copy batch files
Write-Host "نسخ ملفات التشغيل..." -ForegroundColor Cyan
Write-Host "Copying run files..." -ForegroundColor Cyan
Copy-Item "تشغيل البرنامج.bat" $distFolder
Copy-Item "Run Excel Analyzer.bat" $distFolder
Copy-Item "Build.bat" $distFolder

# Create installation instructions
Write-Host "إنشاء تعليمات التثبيت..." -ForegroundColor Cyan
Write-Host "Creating installation instructions..." -ForegroundColor Cyan

$instructions = @"
========================================
   محلل صفقات Excel - Excel Trade Analyzer
========================================

تعليمات التثبيت والتشغيل:
Installation and Running Instructions:

1. متطلبات النظام / System Requirements:
   - Windows 10/11
   - .NET 8.0 Runtime

2. التثبيت / Installation:
   أ. تحميل وتثبيت .NET 8.0 من:
   a. Download and install .NET 8.0 from:
      https://dotnet.microsoft.com/download/dotnet/8.0

   ب. استخراج جميع الملفات إلى مجلد
   b. Extract all files to a folder

3. التشغيل / Running:
   أ. انقر نقراً مزدوجاً على "تشغيل البرنامج.bat"
   a. Double-click "تشغيل البرنامج.bat"

   أو / Or:
   ب. انقر نقراً مزدوجاً على "Run Excel Analyzer.bat"
   b. Double-click "Run Excel Analyzer.bat"

4. البناء من المصدر / Build from Source:
   انقر نقراً مزدوجاً على "Build.bat"
   Double-click "Build.bat"

5. البيانات التجريبية / Test Data:
   استخدم الملفات في مجلد TestData للاختبار
   Use files in TestData folder for testing

========================================
للدعم الفني، راجع ملف README.md
For technical support, see README.md
========================================
"@

$instructions | Out-File -FilePath "$distFolder\تعليمات التثبيت - Installation Instructions.txt" -Encoding UTF8

# Create ZIP file
Write-Host "إنشاء ملف مضغوط..." -ForegroundColor Cyan
Write-Host "Creating ZIP file..." -ForegroundColor Cyan

$zipPath = "ExcelTradeAnalyzer_v1.0.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

try {
    Compress-Archive -Path $distFolder -DestinationPath $zipPath -CompressionLevel Optimal
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "تم إنشاء حزمة التوزيع بنجاح!" -ForegroundColor Green
    Write-Host "Distribution package created successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "الملف المضغوط: $zipPath" -ForegroundColor Yellow
    Write-Host "ZIP file: $zipPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "مجلد التوزيع: $distFolder" -ForegroundColor Yellow
    Write-Host "Distribution folder: $distFolder" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "يمكنك الآن نقل الملف المضغوط إلى أي جهاز آخر" -ForegroundColor Cyan
    Write-Host "You can now transfer the ZIP file to any other computer" -ForegroundColor Cyan
    Write-Host ""
    
    # Show file size
    $zipSize = (Get-Item $zipPath).Length / 1MB
    Write-Host "حجم الملف المضغوط: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Magenta
    Write-Host "ZIP file size: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Magenta
    
} catch {
    Write-Host "خطأ في إنشاء الملف المضغوط: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error creating ZIP file: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
