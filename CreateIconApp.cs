using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

class CreateIconApp
{
    static void Main()
    {
        // Create a simple 32x32 bitmap for the icon
        using var bitmap = new Bitmap(32, 32);
        using var graphics = Graphics.FromImage(bitmap);
        
        // Set background
        graphics.Clear(Color.FromArgb(46, 125, 50));
        
        // Draw Excel-like grid
        using var whiteBrush = new SolidBrush(Color.White);
        graphics.FillRectangle(whiteBrush, 4, 6, 24, 20);
        
        // Draw header
        using var headerBrush = new SolidBrush(Color.FromArgb(76, 175, 80));
        graphics.FillRectangle(headerBrush, 4, 6, 24, 4);
        
        // Draw data bars
        using var blueBrush = new SolidBrush(Color.FromArgb(33, 150, 243));
        using var orangeBrush = new SolidBrush(Color.FromArgb(255, 152, 0));
        
        graphics.FillRectangle(blueBrush, 6, 12, 8, 2);
        graphics.FillRectangle(orangeBrush, 6, 16, 12, 2);
        graphics.FillRectangle(blueBrush, 6, 20, 10, 2);
        
        // Draw calculator icon
        using var calcBrush = new SolidBrush(Color.FromArgb(25, 118, 210));
        graphics.FillRectangle(calcBrush, 20, 16, 6, 6);
        
        // Draw grid lines
        using var gridPen = new Pen(Color.LightGray, 1);
        graphics.DrawLine(gridPen, 4, 10, 28, 10);
        graphics.DrawLine(gridPen, 4, 14, 28, 14);
        graphics.DrawLine(gridPen, 4, 18, 28, 18);
        graphics.DrawLine(gridPen, 4, 22, 28, 22);
        
        // Create directory
        Directory.CreateDirectory("Resources");
        
        // Save as PNG first (simpler)
        bitmap.Save("Resources/icon.png", ImageFormat.Png);
        
        Console.WriteLine("تم إنشاء ملف الأيقونة: Resources/icon.png");
        
        // For ICO, we'll use a simple approach
        // Create a basic ICO file header and data
        CreateSimpleIco(bitmap);
    }
    
    static void CreateSimpleIco(Bitmap bitmap)
    {
        try
        {
            // Convert bitmap to ICO format manually
            using var ms = new MemoryStream();
            bitmap.Save(ms, ImageFormat.Png);
            var pngData = ms.ToArray();
            
            // ICO file header (6 bytes)
            var icoHeader = new byte[]
            {
                0, 0,  // Reserved
                1, 0,  // Type (1 = ICO)
                1, 0   // Number of images
            };
            
            // ICO directory entry (16 bytes)
            var icoEntry = new byte[]
            {
                32,    // Width
                32,    // Height
                0,     // Color count (0 = no palette)
                0,     // Reserved
                1, 0,  // Color planes
                32, 0, // Bits per pixel
                0, 0, 0, 0,  // Image size (will be filled)
                22, 0, 0, 0  // Image offset
            };
            
            // Set image size in entry
            var sizeBytes = BitConverter.GetBytes(pngData.Length);
            Array.Copy(sizeBytes, 0, icoEntry, 8, 4);
            
            // Write ICO file
            using var fs = new FileStream("Resources/icon.ico", FileMode.Create);
            fs.Write(icoHeader, 0, icoHeader.Length);
            fs.Write(icoEntry, 0, icoEntry.Length);
            fs.Write(pngData, 0, pngData.Length);
            
            Console.WriteLine("تم إنشاء ملف الأيقونة: Resources/icon.ico");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إنشاء ملف ICO: {ex.Message}");
        }
    }
}
