using System;
using System.Collections.Generic;
using System.Linq;
using ExcelTradeAnalyzer.Models;

namespace ExcelTradeAnalyzer.Services
{
    /// <summary>
    /// خدمة تحليل الصفقات
    /// </summary>
    public class TradeAnalysisService
    {
        /// <summary>
        /// تحليل قائمة الصفقات وإرجاع النتائج
        /// </summary>
        /// <param name="trades">قائمة الصفقات</param>
        /// <param name="settings">إعدادات التحليل</param>
        /// <returns>نتائج التحليل</returns>
        public TradeAnalysisResult AnalyzeTrades(List<Trade> trades, AnalysisSettings settings)
        {
            if (trades == null || !trades.Any())
            {
                return new TradeAnalysisResult();
            }

            // تصفية الصفقات حسب النطاق الزمني
            var filteredTrades = FilterTradesByDateRange(trades, settings.DateFrom, settings.DateTo);

            // تصفية الصفقات التي تخطت الحد الأدنى للمدة
            var tradesExceedingMinDuration = FilterTradesByDuration(filteredTrades, settings.MinimumDurationMinutes);

            // تجميع الصفقات حسب النوع
            var tradesByType = GroupTradesByType(tradesExceedingMinDuration);

            // حساب الأحجام مع العمولة
            var tradesByTypeWithCommission = CalculateVolumesWithCommission(tradesByType, settings.CommissionRate);

            // إنشاء النتيجة
            var result = new TradeAnalysisResult
            {
                TotalTrades = filteredTrades.Count,
                TradesExceedingFiveMinutes = tradesExceedingMinDuration.Count,
                TradesByType = tradesByType,
                TradesByTypeWithCommission = tradesByTypeWithCommission,
                CommissionRate = settings.CommissionRate,
                DateFrom = settings.DateFrom,
                DateTo = settings.DateTo,
                TotalVolume = tradesByType.Values.Sum(),
                TotalVolumeWithCommission = tradesByTypeWithCommission.Values.Sum()
            };

            return result;
        }

        /// <summary>
        /// تصفية الصفقات حسب النطاق الزمني
        /// </summary>
        private List<Trade> FilterTradesByDateRange(List<Trade> trades, DateTime? dateFrom, DateTime? dateTo)
        {
            var filtered = trades.AsEnumerable();

            if (dateFrom.HasValue)
            {
                filtered = filtered.Where(t => t.OpenTime >= dateFrom.Value);
            }

            if (dateTo.HasValue)
            {
                filtered = filtered.Where(t => t.CloseTime <= dateTo.Value);
            }

            return filtered.ToList();
        }

        /// <summary>
        /// تصفية الصفقات التي تخطت المدة المحددة
        /// </summary>
        private List<Trade> FilterTradesByDuration(List<Trade> trades, double minimumDurationMinutes)
        {
            return trades.Where(t => t.DurationInMinutes > minimumDurationMinutes).ToList();
        }

        /// <summary>
        /// تجميع الصفقات حسب النوع وحساب إجمالي الحجم لكل نوع
        /// </summary>
        private Dictionary<string, decimal> GroupTradesByType(List<Trade> trades)
        {
            return trades
                .GroupBy(t => t.Type, StringComparer.OrdinalIgnoreCase)
                .ToDictionary(
                    g => g.Key,
                    g => g.Sum(t => t.Volume),
                    StringComparer.OrdinalIgnoreCase
                );
        }

        /// <summary>
        /// حساب الأحجام مع العمولة
        /// </summary>
        private Dictionary<string, decimal> CalculateVolumesWithCommission(
            Dictionary<string, decimal> tradesByType, 
            decimal commissionRate)
        {
            return tradesByType.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value * commissionRate,
                StringComparer.OrdinalIgnoreCase
            );
        }

        /// <summary>
        /// الحصول على إحصائيات مفصلة للصفقات
        /// </summary>
        public Dictionary<string, object> GetDetailedStatistics(List<Trade> trades, AnalysisSettings settings)
        {
            var filteredTrades = FilterTradesByDateRange(trades, settings.DateFrom, settings.DateTo);
            var tradesExceedingMinDuration = FilterTradesByDuration(filteredTrades, settings.MinimumDurationMinutes);

            var stats = new Dictionary<string, object>
            {
                ["إجمالي الصفقات"] = filteredTrades.Count,
                ["الصفقات المؤهلة"] = tradesExceedingMinDuration.Count,
                ["نسبة الصفقات المؤهلة"] = filteredTrades.Count > 0 ? 
                    Math.Round((double)tradesExceedingMinDuration.Count / filteredTrades.Count * 100, 2) : 0,
                ["متوسط مدة الصفقة (دقيقة)"] = tradesExceedingMinDuration.Any() ? 
                    Math.Round(tradesExceedingMinDuration.Average(t => t.DurationInMinutes), 2) : 0,
                ["أطول صفقة (دقيقة)"] = tradesExceedingMinDuration.Any() ? 
                    Math.Round(tradesExceedingMinDuration.Max(t => t.DurationInMinutes), 2) : 0,
                ["أقصر صفقة (دقيقة)"] = tradesExceedingMinDuration.Any() ? 
                    Math.Round(tradesExceedingMinDuration.Min(t => t.DurationInMinutes), 2) : 0,
                ["إجمالي الحجم"] = tradesExceedingMinDuration.Sum(t => t.Volume),
                ["متوسط الحجم"] = tradesExceedingMinDuration.Any() ? 
                    Math.Round(tradesExceedingMinDuration.Average(t => t.Volume), 4) : 0
            };

            return stats;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        public List<string> ValidateTradeData(List<Trade> trades)
        {
            var errors = new List<string>();

            if (trades == null || !trades.Any())
            {
                errors.Add("لا توجد صفقات للتحليل");
                return errors;
            }

            var invalidTrades = trades.Where(t => 
                string.IsNullOrEmpty(t.Type) ||
                t.Volume <= 0 ||
                t.OpenTime == default ||
                t.CloseTime == default ||
                t.CloseTime <= t.OpenTime
            ).ToList();

            if (invalidTrades.Any())
            {
                errors.Add($"توجد {invalidTrades.Count} صفقة تحتوي على بيانات غير صحيحة");
            }

            var duplicateIds = trades
                .Where(t => !string.IsNullOrEmpty(t.Id))
                .GroupBy(t => t.Id)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            if (duplicateIds.Any())
            {
                errors.Add($"توجد معرفات مكررة: {string.Join(", ", duplicateIds)}");
            }

            return errors;
        }
    }
}
