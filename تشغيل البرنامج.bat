@echo off
chcp 65001 > nul
title محلل صفقات Excel - Excel Trade Analyzer

echo.
echo ========================================
echo    محلل صفقات Excel - Excel Trade Analyzer
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo Starting the application...
echo.

REM Check if .NET is installed
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 غير مثبت على هذا الجهاز
    echo Error: .NET 8.0 is not installed on this system
    echo.
    echo يرجى تحميل وتثبيت .NET 8.0 من:
    echo Please download and install .NET 8.0 from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM Change to the application directory
cd /d "%~dp0"

REM Run the application
dotnet run --project ExcelTradeAnalyzer.csproj

REM If there's an error, show it
if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo An error occurred while running the application
    echo.
    echo تأكد من وجود جميع الملفات المطلوبة
    echo Make sure all required files are present
    echo.
    pause
)

exit /b %errorlevel%
