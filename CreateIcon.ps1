# Create a simple ICO file using PowerShell
Add-Type -AssemblyName System.Drawing

# Create a bitmap
$bitmap = New-Object System.Drawing.Bitmap(256, 256)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Set high quality rendering
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# Background
$backgroundBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(46, 125, 50))
$graphics.FillRectangle($backgroundBrush, 0, 0, 256, 256)

# Excel-like grid background
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.FillRectangle($whiteBrush, 40, 60, 176, 136)

# Header
$headerBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(76, 175, 80))
$graphics.FillRectangle($headerBrush, 40, 60, 176, 30)

# Data bars
$blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(33, 150, 243))
$orangeBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 152, 0))
$purpleBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(156, 39, 176))

$graphics.FillRectangle($blueBrush, 50, 100, 60, 12)
$graphics.FillRectangle($orangeBrush, 50, 130, 80, 12)
$graphics.FillRectangle($purpleBrush, 50, 160, 70, 12)

# Calculator icon
$calcBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(25, 118, 210))
$graphics.FillRectangle($calcBrush, 170, 140, 40, 40)

# Calculator buttons
$buttonBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.FillRectangle($buttonBrush, 175, 145, 8, 8)
$graphics.FillRectangle($buttonBrush, 187, 145, 8, 8)
$graphics.FillRectangle($buttonBrush, 199, 145, 8, 8)
$graphics.FillRectangle($buttonBrush, 175, 157, 8, 8)
$graphics.FillRectangle($buttonBrush, 187, 157, 8, 8)
$graphics.FillRectangle($buttonBrush, 199, 157, 8, 8)

# Equal button
$equalBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(76, 175, 80))
$graphics.FillRectangle($equalBrush, 175, 169, 32, 8)

# Title
$font = New-Object System.Drawing.Font("Arial", 14, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$stringFormat = New-Object System.Drawing.StringFormat
$stringFormat.Alignment = [System.Drawing.StringAlignment]::Center
$graphics.DrawString("Excel Analyzer", $font, $textBrush, 128, 25, $stringFormat)

# Grid lines
$gridPen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(224, 224, 224), 1)
$graphics.DrawLine($gridPen, 40, 90, 216, 90)
$graphics.DrawLine($gridPen, 40, 120, 216, 120)
$graphics.DrawLine($gridPen, 40, 150, 216, 150)
$graphics.DrawLine($gridPen, 40, 180, 216, 180)
$graphics.DrawLine($gridPen, 80, 60, 80, 196)
$graphics.DrawLine($gridPen, 120, 60, 120, 196)
$graphics.DrawLine($gridPen, 160, 60, 160, 196)

# Clean up
$graphics.Dispose()

# Create directory if it doesn't exist
$resourcesDir = "Resources"
if (!(Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Save as ICO
$iconPath = Join-Path $resourcesDir "icon.ico"
$icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
$fileStream = [System.IO.FileStream]::new($iconPath, [System.IO.FileMode]::Create)
$icon.Save($fileStream)
$fileStream.Close()

# Clean up
$bitmap.Dispose()
$icon.Dispose()

Write-Host "تم إنشاء الأيقونة في: $iconPath"
