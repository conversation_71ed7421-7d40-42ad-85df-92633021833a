using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using OfficeOpenXml;
using ExcelTradeAnalyzer.Models;

namespace ExcelTradeAnalyzer.Services
{
    /// <summary>
    /// خدمة قراءة وكتابة ملفات Excel
    /// </summary>
    public class ExcelService
    {
        public ExcelService()
        {
            // تعيين سياق الترخيص لـ EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// قراءة الصفقات من ملف Excel
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="settings">إعدادات القراءة</param>
        /// <returns>قائمة الصفقات</returns>
        public List<Trade> ReadTradesFromExcel(string filePath, AnalysisSettings settings)
        {
            var trades = new List<Trade>();

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"الملف غير موجود: {filePath}");
            }

            using var package = new ExcelPackage(new FileInfo(filePath));
            
            // اختيار ورقة العمل
            ExcelWorksheet worksheet;
            if (!string.IsNullOrEmpty(settings.WorksheetName))
            {
                worksheet = package.Workbook.Worksheets[settings.WorksheetName];
                if (worksheet == null)
                {
                    throw new ArgumentException($"ورقة العمل '{settings.WorksheetName}' غير موجودة");
                }
            }
            else
            {
                worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    throw new ArgumentException("لا توجد أوراق عمل في الملف");
                }
            }

            // قراءة العناوين
            var headers = ReadHeaders(worksheet, settings.HeaderRow);
            
            // قراءة البيانات
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            
            for (int row = settings.DataStartRow; row <= rowCount; row++)
            {
                try
                {
                    var trade = ReadTradeFromRow(worksheet, row, headers);
                    if (trade != null)
                    {
                        trades.Add(trade);
                    }
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ وتجاهل الصف
                    Console.WriteLine($"خطأ في قراءة الصف {row}: {ex.Message}");
                }
            }

            return trades;
        }

        /// <summary>
        /// قراءة العناوين من الصف المحدد
        /// </summary>
        private Dictionary<string, int> ReadHeaders(ExcelWorksheet worksheet, int headerRow)
        {
            var headers = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var columnCount = worksheet.Dimension?.Columns ?? 0;

            for (int col = 1; col <= columnCount; col++)
            {
                var headerValue = worksheet.Cells[headerRow, col].Text?.Trim();
                if (!string.IsNullOrEmpty(headerValue))
                {
                    headers[headerValue] = col;
                }
            }

            return headers;
        }

        /// <summary>
        /// قراءة صفقة من صف محدد
        /// </summary>
        private Trade? ReadTradeFromRow(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers)
        {
            try
            {
                var trade = new Trade();

                // قراءة البيانات الأساسية
                trade.Id = GetCellValue(worksheet, row, headers, "Id", "معرف", "رقم");
                trade.Type = GetCellValue(worksheet, row, headers, "Type", "نوع", "النوع");
                trade.Symbol = GetCellValue(worksheet, row, headers, "Symbol", "رمز", "الرمز");
                trade.Comment = GetCellValue(worksheet, row, headers, "Comment", "تعليق", "ملاحظة");

                // قراءة الأرقام
                trade.Volume = GetDecimalValue(worksheet, row, headers, "Volume", "حجم", "الحجم");
                trade.OpenPrice = GetDecimalValue(worksheet, row, headers, "OpenPrice", "سعر الافتتاح", "سعر الفتح");
                trade.ClosePrice = GetDecimalValue(worksheet, row, headers, "ClosePrice", "سعر الإغلاق", "سعر الإغلاق");
                trade.ProfitLoss = GetDecimalValue(worksheet, row, headers, "ProfitLoss", "الربح", "الخسارة", "النتيجة");

                // قراءة التواريخ
                trade.OpenTime = GetDateTimeValue(worksheet, row, headers, "OpenTime", "وقت الافتتاح", "وقت الفتح");
                trade.CloseTime = GetDateTimeValue(worksheet, row, headers, "CloseTime", "وقت الإغلاق", "وقت الإغلاق");

                // التحقق من صحة البيانات الأساسية
                if (string.IsNullOrEmpty(trade.Type) || trade.Volume <= 0 || 
                    trade.OpenTime == default || trade.CloseTime == default)
                {
                    return null;
                }

                return trade;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// الحصول على قيمة نصية من الخلية
        /// </summary>
        private string GetCellValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, params string[] possibleHeaders)
        {
            foreach (var header in possibleHeaders)
            {
                if (headers.TryGetValue(header, out int col))
                {
                    return worksheet.Cells[row, col].Text?.Trim() ?? string.Empty;
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// الحصول على قيمة رقمية من الخلية
        /// </summary>
        private decimal GetDecimalValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, params string[] possibleHeaders)
        {
            foreach (var header in possibleHeaders)
            {
                if (headers.TryGetValue(header, out int col))
                {
                    var cellValue = worksheet.Cells[row, col].Value;
                    if (cellValue != null && decimal.TryParse(cellValue.ToString(), out decimal result))
                    {
                        return result;
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// الحصول على قيمة تاريخ من الخلية
        /// </summary>
        private DateTime GetDateTimeValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, params string[] possibleHeaders)
        {
            foreach (var header in possibleHeaders)
            {
                if (headers.TryGetValue(header, out int col))
                {
                    var cellValue = worksheet.Cells[row, col].Value;
                    if (cellValue != null)
                    {
                        if (cellValue is DateTime dateTime)
                        {
                            return dateTime;
                        }
                        if (DateTime.TryParse(cellValue.ToString(), out DateTime result))
                        {
                            return result;
                        }
                    }
                }
            }
            return default;
        }

        /// <summary>
        /// كتابة نتائج التحليل إلى ملف Excel جديد
        /// </summary>
        /// <param name="result">نتائج التحليل</param>
        /// <param name="outputPath">مسار الملف المخرج</param>
        public void WriteAnalysisResultToExcel(TradeAnalysisResult result, string outputPath)
        {
            using var package = new ExcelPackage();

            // إنشاء ورقة عمل للملخص
            var summarySheet = package.Workbook.Worksheets.Add("ملخص التحليل");
            WriteSummarySheet(summarySheet, result);

            // إنشاء ورقة عمل للتفاصيل حسب النوع
            var detailsSheet = package.Workbook.Worksheets.Add("التفاصيل حسب النوع");
            WriteDetailsSheet(detailsSheet, result);

            // حفظ الملف
            var fileInfo = new FileInfo(outputPath);
            package.SaveAs(fileInfo);
        }

        /// <summary>
        /// كتابة ورقة الملخص
        /// </summary>
        private void WriteSummarySheet(ExcelWorksheet worksheet, TradeAnalysisResult result)
        {
            int row = 1;

            // العنوان
            worksheet.Cells[row, 1].Value = "تقرير تحليل الصفقات";
            worksheet.Cells[row, 1].Style.Font.Size = 16;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            row += 2;

            // معلومات عامة
            worksheet.Cells[row, 1].Value = "تاريخ التقرير:";
            worksheet.Cells[row, 2].Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            row++;

            if (result.DateFrom.HasValue)
            {
                worksheet.Cells[row, 1].Value = "من تاريخ:";
                worksheet.Cells[row, 2].Value = result.DateFrom.Value.ToString("yyyy-MM-dd");
                row++;
            }

            if (result.DateTo.HasValue)
            {
                worksheet.Cells[row, 1].Value = "إلى تاريخ:";
                worksheet.Cells[row, 2].Value = result.DateTo.Value.ToString("yyyy-MM-dd");
                row++;
            }

            worksheet.Cells[row, 1].Value = "معدل العمولة:";
            worksheet.Cells[row, 2].Value = result.CommissionRate;
            worksheet.Cells[row, 2].Style.Numberformat.Format = "0.0000";
            row += 2;

            // الإحصائيات
            worksheet.Cells[row, 1].Value = "الإحصائيات العامة";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            row++;

            worksheet.Cells[row, 1].Value = "إجمالي الصفقات:";
            worksheet.Cells[row, 2].Value = result.TotalTrades;
            row++;

            worksheet.Cells[row, 1].Value = "الصفقات التي تخطت 5 دقائق:";
            worksheet.Cells[row, 2].Value = result.TradesExceedingFiveMinutes;
            row++;

            worksheet.Cells[row, 1].Value = "إجمالي الحجم:";
            worksheet.Cells[row, 2].Value = result.TotalVolume;
            worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.0000";
            row++;

            worksheet.Cells[row, 1].Value = "إجمالي الحجم بعد العمولة:";
            worksheet.Cells[row, 2].Value = result.TotalVolumeWithCommission;
            worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.0000";
            row += 2;

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 25;
            worksheet.Column(2).Width = 20;
        }

        /// <summary>
        /// كتابة ورقة التفاصيل
        /// </summary>
        private void WriteDetailsSheet(ExcelWorksheet worksheet, TradeAnalysisResult result)
        {
            int row = 1;

            // العناوين
            worksheet.Cells[row, 1].Value = "نوع الصفقة";
            worksheet.Cells[row, 2].Value = "إجمالي الحجم";
            worksheet.Cells[row, 3].Value = "الحجم بعد العمولة";
            worksheet.Cells[row, 4].Value = "النسبة المئوية";

            // تنسيق العناوين
            using (var range = worksheet.Cells[row, 1, row, 4])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            row++;

            // البيانات
            foreach (var trade in result.TradesByType.OrderByDescending(t => t.Value))
            {
                worksheet.Cells[row, 1].Value = trade.Key;
                worksheet.Cells[row, 2].Value = trade.Value;
                worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.0000";

                if (result.TradesByTypeWithCommission.TryGetValue(trade.Key, out decimal volumeWithCommission))
                {
                    worksheet.Cells[row, 3].Value = volumeWithCommission;
                    worksheet.Cells[row, 3].Style.Numberformat.Format = "#,##0.0000";
                }

                // حساب النسبة المئوية
                if (result.TotalVolume > 0)
                {
                    var percentage = (trade.Value / result.TotalVolume) * 100;
                    worksheet.Cells[row, 4].Value = percentage;
                    worksheet.Cells[row, 4].Style.Numberformat.Format = "0.00%";
                }

                row++;
            }

            // إضافة صف الإجمالي
            worksheet.Cells[row, 1].Value = "الإجمالي";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 2].Value = result.TotalVolume;
            worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.0000";
            worksheet.Cells[row, 2].Style.Font.Bold = true;
            worksheet.Cells[row, 3].Value = result.TotalVolumeWithCommission;
            worksheet.Cells[row, 3].Style.Numberformat.Format = "#,##0.0000";
            worksheet.Cells[row, 3].Style.Font.Bold = true;
            worksheet.Cells[row, 4].Value = 1.0;
            worksheet.Cells[row, 4].Style.Numberformat.Format = "0.00%";
            worksheet.Cells[row, 4].Style.Font.Bold = true;

            // تنسيق الأعمدة
            worksheet.Column(1).Width = 20;
            worksheet.Column(2).Width = 15;
            worksheet.Column(3).Width = 20;
            worksheet.Column(4).Width = 15;

            // إضافة حدود للجدول
            using (var range = worksheet.Cells[1, 1, row, 4])
            {
                range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            }
        }
    }
}
